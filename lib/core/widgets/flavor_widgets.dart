import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../flavors.dart';
import '../theme/flavor_theme.dart';
import '../utils/flavor_assets.dart';

/// Factory class for creating flavor-specific widgets
/// Provides different widget implementations based on app flavor
class FlavorWidgets {
  
  /// Create flavor-specific app bar
  static PreferredSizeWidget createAppBar({
    required String title,
    List<Widget>? actions,
    Widget? leading,
    bool automaticallyImplyLeading = true,
  }) {
    switch (F.appFlavor) {
      case Flavor.sis:
        return AppBar(
          title: Text(
            title,
            style: FlavorTheme.getHeadingStyle(),
          ),
          backgroundColor: FlavorTheme.getPrimaryColor(),
          foregroundColor: Colors.white,
          elevation: 2,
          centerTitle: true,
          actions: actions,
          leading: leading,
          automaticallyImplyLeading: automaticallyImplyLeading,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(
              bottom: Radius.circular(16),
            ),
          ),
        );
      default:
        return AppBar(
          title: Text(title),
          actions: actions,
          leading: leading,
          automaticallyImplyLeading: automaticallyImplyLeading,
        );
    }
  }

  /// Create flavor-specific navigation icon
  static Widget createNavIcon({
    required String iconType,
    required bool isSelected,
    double? size,
  }) {
    String iconPath;
    switch (iconType) {
      case 'home':
        iconPath = FlavorAssets.homeIcon;
        break;
      case 'profile':
        iconPath = FlavorAssets.profileIcon;
        break;
      case 'community':
        iconPath = FlavorAssets.communityIcon;
        break;
      case 'buy':
        iconPath = FlavorAssets.buyIcon;
        break;
      case 'quote':
        iconPath = FlavorAssets.quoteIcon;
        break;
      default:
        iconPath = 'assets/svg/$iconType.svg';
    }

    return SvgPicture.asset(
      iconPath,
      width: size ?? 24,
      height: size ?? 24,
      color: isSelected 
          ? FlavorTheme.getPrimaryColor()
          : Colors.grey,
    );
  }

  /// Create flavor-specific action button
  static Widget createActionButton({
    required String label,
    required VoidCallback onPressed,
    String? iconPath,
    bool isPrimary = true,
  }) {
    switch (F.appFlavor) {
      case Flavor.sis:
        return Container(
          decoration: FlavorTheme.getCardDecoration(),
          child: ElevatedButton.icon(
            onPressed: onPressed,
            style: FlavorTheme.getButtonStyle(),
            icon: iconPath != null 
                ? SvgPicture.asset(iconPath, width: 20, height: 20, color: Colors.white)
                : const SizedBox.shrink(),
            label: Text(label),
          ),
        );
      default:
        return ElevatedButton(
          onPressed: onPressed,
          child: Text(label),
        );
    }
  }

  /// Create flavor-specific card widget
  static Widget createCard({
    required Widget child,
    EdgeInsets? padding,
    EdgeInsets? margin,
  }) {
    return Container(
      margin: margin ?? EdgeInsets.all(FlavorTheme.getSpacing()),
      padding: padding ?? EdgeInsets.all(FlavorTheme.getSpacing()),
      decoration: FlavorTheme.getCardDecoration(),
      child: child,
    );
  }

  /// Create flavor-specific list tile
  static Widget createListTile({
    required String title,
    String? subtitle,
    Widget? leading,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    switch (F.appFlavor) {
      case Flavor.sis:
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(FlavorTheme.getBorderRadius()),
            color: Colors.white,
            border: Border.all(
              color: FlavorTheme.getPrimaryColor().withOpacity(0.2),
            ),
          ),
          child: ListTile(
            title: Text(
              title,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: FlavorTheme.getPrimaryColor(),
              ),
            ),
            subtitle: subtitle != null ? Text(subtitle) : null,
            leading: leading,
            trailing: trailing,
            onTap: onTap,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(FlavorTheme.getBorderRadius()),
            ),
          ),
        );
      default:
        return ListTile(
          title: Text(title),
          subtitle: subtitle != null ? Text(subtitle) : null,
          leading: leading,
          trailing: trailing,
          onTap: onTap,
        );
    }
  }

  /// Create flavor-specific bottom navigation bar
  static Widget createBottomNavBar({
    required int currentIndex,
    required Function(int) onTap,
    required List<BottomNavigationBarItem> items,
  }) {
    switch (F.appFlavor) {
      case Flavor.sis:
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: FlavorTheme.getGradientColors(),
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            borderRadius: const BorderRadius.vertical(
              top: Radius.circular(20),
            ),
          ),
          child: BottomNavigationBar(
            currentIndex: currentIndex,
            onTap: onTap,
            items: items,
            type: BottomNavigationBarType.fixed,
            backgroundColor: Colors.transparent,
            elevation: 0,
            selectedItemColor: Colors.white,
            unselectedItemColor: Colors.white70,
          ),
        );
      default:
        return BottomNavigationBar(
          currentIndex: currentIndex,
          onTap: onTap,
          items: items,
        );
    }
  }
}
