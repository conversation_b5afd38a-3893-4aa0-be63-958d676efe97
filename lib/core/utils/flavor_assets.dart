import '../../flavors.dart';
import '../constants/assets.dart';

/// Utility class for managing flavor-specific assets
/// Provides methods to get the correct asset path based on current flavor
class FlavorAssets {
  
  /// Get flavor-specific icon path with fallback to default
  /// 
  /// Usage: FlavorAssets.getIcon('home.svg')
  /// Returns: 'assets/svg/sis/home.svg' for SIS flavor, 'assets/svg/home.svg' for others
  static String getIcon(String iconName) {
    final flavor = F.appFlavor;
    switch (flavor) {
      case Flavor.sis:
        return 'assets/svg/sis/$iconName';
      case Flavor.cfroex:
        return 'assets/svg/cfroex/$iconName';
      case Flavor.ncm:
        return 'assets/svg/ncm/$iconName';
      case Flavor.sf_app:
        return 'assets/svg/$iconName';
    }
  }

  /// Get flavor-specific image path with fallback to default
  static String getImage(String imageName) {
    final flavor = F.appFlavor;
    switch (flavor) {
      case Flavor.sis:
        return 'assets/images/sis/$imageName';
      case Flavor.cfroex:
        return 'assets/images/cfroex/$imageName';
      case Flavor.ncm:
        return 'assets/images/ncm/$imageName';
      case Flavor.sf_app:
        return 'assets/images/$imageName';
    }
  }

  /// Get flavor-specific PNG icon path with fallback to default
  static String getPngIcon(String iconName) {
    final flavor = F.appFlavor;
    switch (flavor) {
      case Flavor.sis:
        return 'assets/icons/sis/$iconName';
      case Flavor.cfroex:
        return 'assets/icons/cfroex/$iconName';
      case Flavor.ncm:
        return 'assets/icons/ncm/$iconName';
      case Flavor.sf_app:
        return 'assets/icons/$iconName';
    }
  }

  // Flavor-aware navigation icons
  static String get homeIcon => getIcon('home.svg');
  static String get contractIcon => getIcon('contracts.svg');
  static String get communityIcon => getIcon('community.svg');
  static String get profileIcon => getIcon('profile.svg');
  static String get buyIcon => getIcon('buy.svg');
  static String get quoteIcon => getIcon('quote.svg');
  
  // Flavor-aware action icons
  static String get depositIcon => getIcon('deposit.svg');
  static String get withdrawIcon => getIcon('withdraw2.svg');
  static String get transferIcon => getIcon('transfer.svg');
  static String get recordsIcon => getIcon('record.svg');
  static String get supportIcon => getIcon('support.svg');
  static String get chatIcon => getIcon('chat.svg');
  
  // Flavor-aware level icons
  static String get level0Icon => getPngIcon('level0.png');
  static String get level1Icon => getPngIcon('level1.png');
  static String get level2Icon => getPngIcon('level2.png');
  static String get level3Icon => getPngIcon('level3.png');
  static String get level4Icon => getPngIcon('level4.png');
  static String get level5Icon => getPngIcon('level5.png');
}
